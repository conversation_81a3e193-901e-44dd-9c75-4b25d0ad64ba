# PowerShell script to fix SSL issues by downgrading Node.js
# Run this script as Administrator

Write-Host "🔧 SSL Fix Script - Downgrading Node.js to v20.18.0" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 1: Check if NVM is installed
Write-Host "`n📋 Step 1: Checking for Node Version Manager..." -ForegroundColor Cyan
try {
    $nvmVersion = nvm version 2>$null
    if ($nvmVersion) {
        Write-Host "✅ NVM is already installed: $nvmVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ NVM not found. Installing NVM for Windows..." -ForegroundColor Yellow
    
    # Download and install NVM
    $nvmUrl = "https://github.com/coreybutler/nvm-windows/releases/download/1.1.12/nvm-setup.exe"
    $nvmInstaller = "$env:TEMP\nvm-setup.exe"
    
    Write-Host "📥 Downloading NVM installer..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $nvmUrl -OutFile $nvmInstaller
    
    Write-Host "🚀 Running NVM installer..." -ForegroundColor Yellow
    Start-Process -FilePath $nvmInstaller -Wait
    
    Write-Host "✅ NVM installation completed. Please restart PowerShell and run this script again." -ForegroundColor Green
    Read-Host "Press Enter to exit"
    exit 0
}

# Step 2: Install Node.js v20.18.0
Write-Host "`n📋 Step 2: Installing Node.js v20.18.0..." -ForegroundColor Cyan
try {
    # Check current Node.js version
    $currentNode = node --version 2>$null
    Write-Host "Current Node.js version: $currentNode" -ForegroundColor Yellow
    
    # Install Node.js v20.18.0
    Write-Host "📥 Installing Node.js v20.18.0..." -ForegroundColor Yellow
    nvm install 20.18.0
    
    # Switch to Node.js v20.18.0
    Write-Host "🔄 Switching to Node.js v20.18.0..." -ForegroundColor Yellow
    nvm use 20.18.0
    
    # Verify installation
    $newNode = node --version
    Write-Host "✅ Node.js version after switch: $newNode" -ForegroundColor Green
    
    if ($newNode -like "*v20.18.0*") {
        Write-Host "✅ Successfully switched to Node.js v20.18.0!" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to switch to Node.js v20.18.0" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error installing Node.js: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Clean and reinstall dependencies
Write-Host "`n📋 Step 3: Cleaning and reinstalling dependencies..." -ForegroundColor Cyan
try {
    # Navigate to project directory
    $projectPath = Get-Location
    Write-Host "Project directory: $projectPath" -ForegroundColor Yellow
    
    # Remove node_modules and package-lock.json
    if (Test-Path "node_modules") {
        Write-Host "🗑️ Removing node_modules..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force "node_modules"
    }
    
    if (Test-Path "package-lock.json") {
        Write-Host "🗑️ Removing package-lock.json..." -ForegroundColor Yellow
        Remove-Item -Force "package-lock.json"
    }
    
    # Clear npm cache
    Write-Host "🧹 Clearing npm cache..." -ForegroundColor Yellow
    npm cache clean --force
    
    # Reinstall dependencies
    Write-Host "📦 Installing dependencies with Node.js v20..." -ForegroundColor Yellow
    npm install
    
    Write-Host "✅ Dependencies reinstalled successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Error during dependency installation: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Test the fix
Write-Host "`n📋 Step 4: Testing the SSL fix..." -ForegroundColor Cyan
Write-Host "🚀 Starting development server to test..." -ForegroundColor Yellow
Write-Host ""
Write-Host "After the server starts, test these URLs:" -ForegroundColor Green
Write-Host "1. R2 Test: http://localhost:3000/api/test-r2-aws-sdk" -ForegroundColor White
Write-Host "2. SSL Services: http://localhost:3000/api/test-ssl-services" -ForegroundColor White
Write-Host ""
Write-Host "✅ SSL fix completed! Node.js downgraded to v20.18.0" -ForegroundColor Green
Write-Host "🎉 Your R2 uploads should now work!" -ForegroundColor Green

# Start the development server
Write-Host "`nStarting development server..." -ForegroundColor Cyan
npm run dev
