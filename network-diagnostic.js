const dns = require('dns');
const net = require('net');

const R2_ACCOUNT_ID = 'e6e8f16fca044c21183dbe6f5007951f';
const hostname = `${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`;

console.log('=== Network Diagnostic for R2 ===\n');
console.log('Testing hostname:', hostname);

// Test 1: DNS Resolution
async function testDNSResolution() {
  console.log('\nTest 1: DNS Resolution');
  return new Promise((resolve) => {
    dns.resolve4(hostname, (err, addresses) => {
      if (err) {
        console.log('❌ DNS resolution failed:', err.message);
        console.log('  Error code:', err.code);
        resolve(false);
      } else {
        console.log('✅ DNS resolution successful');
        console.log('  IP addresses:', addresses);
        resolve(true);
      }
    });
  });
}

// Test 2: Basic TCP connection on port 443
async function testTCPConnection() {
  console.log('\nTest 2: TCP Connection to port 443');
  return new Promise((resolve) => {
    const socket = new net.Socket();
    
    socket.setTimeout(10000);
    
    socket.connect(443, hostname, () => {
      console.log('✅ TCP connection successful');
      socket.destroy();
      resolve(true);
    });
    
    socket.on('error', (err) => {
      console.log('❌ TCP connection failed:', err.message);
      console.log('  Error code:', err.code);
      resolve(false);
    });
    
    socket.on('timeout', () => {
      console.log('❌ TCP connection timeout');
      socket.destroy();
      resolve(false);
    });
  });
}

// Test 3: Test alternative R2 endpoints
async function testAlternativeEndpoints() {
  console.log('\nTest 3: Testing alternative R2 endpoints');
  
  const endpoints = [
    `${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    'r2.cloudflarestorage.com',
    's3.amazonaws.com', // Just to test if general S3 works
  ];
  
  for (const endpoint of endpoints) {
    console.log(`  Testing ${endpoint}...`);
    
    const dnsSuccess = await new Promise((resolve) => {
      dns.resolve4(endpoint, (err, addresses) => {
        if (err) {
          console.log(`  ❌ ${endpoint} - DNS failed:`, err.code);
          resolve(false);
        } else {
          console.log(`  ✅ ${endpoint} - DNS successful:`, addresses[0]);
          resolve(true);
        }
      });
    });
    
    if (dnsSuccess) {
      const tcpSuccess = await new Promise((resolve) => {
        const socket = new net.Socket();
        socket.setTimeout(5000);
        
        socket.connect(443, endpoint, () => {
          console.log(`  ✅ ${endpoint} - TCP connection successful`);
          socket.destroy();
          resolve(true);
        });
        
        socket.on('error', (err) => {
          console.log(`  ❌ ${endpoint} - TCP failed:`, err.code);
          resolve(false);
        });
        
        socket.on('timeout', () => {
          console.log(`  ❌ ${endpoint} - TCP timeout`);
          socket.destroy();
          resolve(false);
        });
      });
    }
  }
}

// Test 4: Check environment variables
function testEnvironmentVariables() {
  console.log('\nTest 4: Environment Variables');
  
  const envVars = [
    'R2_ACCOUNT_ID',
    'R2_ACCESS_KEY_ID', 
    'R2_SECRET_ACCESS_KEY',
    'R2_BUCKET_NAME'
  ];
  
  for (const envVar of envVars) {
    const value = process.env[envVar];
    if (value) {
      console.log(`✅ ${envVar} is set (${value.length} chars)`);
    } else {
      console.log(`❌ ${envVar} is not set`);
    }
  }
}

// Run all tests
async function runNetworkDiagnostics() {
  await testDNSResolution();
  await testTCPConnection();
  await testAlternativeEndpoints();
  testEnvironmentVariables();
  
  console.log('\n=== Network Diagnostic Complete ===');
}

runNetworkDiagnostics().catch(console.error);
