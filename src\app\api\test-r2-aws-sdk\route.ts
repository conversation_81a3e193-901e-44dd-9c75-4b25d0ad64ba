import { NextRequest, NextResponse } from "next/server";
import {
  S3Client,
  PutObjectCommand,
  ObjectCannedACL,
} from "@aws-sdk/client-s3";
import { NodeHttpHandler } from "@aws-sdk/node-http-handler";
import https from "https";
import crypto from "crypto";

// Create multiple HTTPS agents to test different SSL configurations
const httpsAgents = [
  // Agent 1: Disable SSL verification (for testing only)
  new https.Agent({
    rejectUnauthorized: false,
    keepAlive: true,
    timeout: 30000,
  }),

  // Agent 2: TLS 1.2 with relaxed ciphers
  new https.Agent({
    minVersion: "TLSv1.2",
    maxVersion: "TLSv1.2",
    ciphers: 'HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA',
    rejectUnauthorized: true,
    keepAlive: true,
    timeout: 30000,
  }),

  // Agent 3: Default with legacy support
  new https.Agent({
    secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
    rejectUnauthorized: true,
    keepAlive: true,
    timeout: 30000,
  }),
];

export async function GET(request: NextRequest) {
  const testContent = `This is a test file uploaded at ${new Date().toISOString()}`;
  const testFilePath = "test/aws-sdk-test-file.txt";

  console.log("Testing R2 connection with multiple SSL configurations...");

  // Try each HTTPS agent configuration
  for (let i = 0; i < httpsAgents.length; i++) {
    const agent = httpsAgents[i];
    const agentName = i === 0 ? "No SSL Verification" : i === 1 ? "TLS 1.2 Relaxed" : "Legacy Support";

    console.log(`\nTrying Agent ${i + 1}: ${agentName}`);

    try {
      // Create S3 client with current agent
      const s3Client = new S3Client({
        region: "auto",
        endpoint: process.env.R2_ENDPOINT_URL,
        credentials: {
          accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
          secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
        },
        requestHandler: new NodeHttpHandler({
          httpsAgent: agent,
          connectionTimeout: 30000,
          socketTimeout: 30000,
        }),
      });

      // Upload the file to R2
      const uploadParams = {
        Bucket: process.env.R2_BUCKET_NAME,
        Key: testFilePath,
        Body: testContent,
        ContentType: "text/plain",
        ACL: "public-read" as ObjectCannedACL,
      };

      const uploadCommand = new PutObjectCommand(uploadParams);
      const uploadResult = await s3Client.send(uploadCommand);

      console.log(`✅ Success with Agent ${i + 1}: ${agentName}`);

      // Generate the public URL
      const publicUrl = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL}/${testFilePath}`;

      return NextResponse.json({
        success: true,
        message: `✅ AWS SDK connection to R2 successful with ${agentName}!`,
        workingAgent: agentName,
        agentIndex: i,
        publicUrl,
        uploadResult,
        testFilePath,
        env: {
          R2_BUCKET_NAME: process.env.R2_BUCKET_NAME,
          R2_ENDPOINT_URL: process.env.R2_ENDPOINT_URL?.replace(/\/\/.*@/, "//[REDACTED]@"),
          NEXT_PUBLIC_R2_PUBLIC_URL: process.env.NEXT_PUBLIC_R2_PUBLIC_URL,
        },
      });

    } catch (error) {
      console.log(`❌ Agent ${i + 1} (${agentName}) failed:`, error instanceof Error ? error.message : error);

      // Continue to next agent
      if (i === httpsAgents.length - 1) {
        // This was the last agent, return error
        return NextResponse.json(
          {
            success: false,
            message: "❌ All SSL configurations failed",
            error: error instanceof Error ? error.message : "Unknown error",
            testedAgents: ["No SSL Verification", "TLS 1.2 Relaxed", "Legacy Support"],
          },
          { status: 500 }
        );
      }
    }
  }
}
