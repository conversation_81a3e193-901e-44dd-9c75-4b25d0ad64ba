import { NextRequest, NextResponse } from "next/server";
import https from "https";
import crypto from "crypto";

// Test SSL connectivity to all external services used by the app
const EXTERNAL_SERVICES = [
  {
    name: "Cloudflare R2",
    hostname: "e6e8f16fca044c21183dbe6f5007951f.r2.cloudflarestorage.com",
    port: 443,
    path: "/",
    critical: true
  },
  {
    name: "MongoDB Atlas",
    hostname: "cluster0.rgfqa.mongodb.net",
    port: 443,
    path: "/",
    critical: true
  },
  {
    name: "Razorpay API",
    hostname: "api.razorpay.com",
    port: 443,
    path: "/v1",
    critical: true
  },
  {
    name: "Razorpay Checkout",
    hostname: "checkout.razorpay.com",
    port: 443,
    path: "/",
    critical: false
  },
  {
    name: "Google APIs",
    hostname: "www.googleapis.com",
    port: 443,
    path: "/",
    critical: true
  },
  {
    name: "Google OAuth",
    hostname: "accounts.google.com",
    port: 443,
    path: "/",
    critical: true
  },
  {
    name: "Resend API",
    hostname: "api.resend.com",
    port: 443,
    path: "/",
    critical: false
  }
];

// Different SSL configurations to test
const SSL_CONFIGS = [
  {
    name: "Default Node.js",
    config: {}
  },
  {
    name: "TLS 1.2 Only",
    config: {
      minVersion: "TLSv1.2",
      maxVersion: "TLSv1.2"
    }
  },
  {
    name: "TLS 1.3 Only", 
    config: {
      minVersion: "TLSv1.3",
      maxVersion: "TLSv1.3"
    }
  },
  {
    name: "Legacy Compatible",
    config: {
      secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
      ciphers: 'HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA'
    }
  },
  {
    name: "No SSL Verification",
    config: {
      rejectUnauthorized: false
    }
  }
];

async function testServiceSSL(service: typeof EXTERNAL_SERVICES[0], sslConfig: typeof SSL_CONFIGS[0]) {
  return new Promise<{
    success: boolean;
    protocol?: string;
    cipher?: string;
    error?: string;
    responseTime: number;
  }>((resolve) => {
    const startTime = Date.now();
    
    const agent = new https.Agent({
      ...sslConfig.config,
      timeout: 10000,
      keepAlive: false
    });

    const req = https.request({
      hostname: service.hostname,
      port: service.port,
      path: service.path,
      method: 'HEAD',
      agent: agent,
      timeout: 10000,
    }, (res) => {
      const responseTime = Date.now() - startTime;
      resolve({
        success: true,
        protocol: res.socket?.getProtocol?.(),
        cipher: res.socket?.getCipher?.()?.name,
        responseTime
      });
      res.destroy();
    });

    req.on('error', (error) => {
      const responseTime = Date.now() - startTime;
      resolve({
        success: false,
        error: error.message,
        responseTime
      });
    });

    req.on('timeout', () => {
      const responseTime = Date.now() - startTime;
      req.destroy();
      resolve({
        success: false,
        error: 'Connection timeout',
        responseTime
      });
    });

    req.end();
  });
}

export async function GET(request: NextRequest) {
  console.log("🔍 Starting comprehensive SSL service test...");
  
  const results: any = {
    nodeVersion: process.version,
    testTimestamp: new Date().toISOString(),
    services: {},
    summary: {
      totalServices: EXTERNAL_SERVICES.length,
      criticalServices: EXTERNAL_SERVICES.filter(s => s.critical).length,
      workingServices: 0,
      failingServices: 0,
      criticalFailures: 0
    }
  };

  // Test each service with each SSL configuration
  for (const service of EXTERNAL_SERVICES) {
    console.log(`Testing ${service.name}...`);
    
    results.services[service.name] = {
      hostname: service.hostname,
      critical: service.critical,
      configs: {}
    };

    let serviceWorking = false;

    for (const sslConfig of SSL_CONFIGS) {
      console.log(`  - Testing with ${sslConfig.name}...`);
      
      try {
        const result = await testServiceSSL(service, sslConfig);
        results.services[service.name].configs[sslConfig.name] = result;
        
        if (result.success && !serviceWorking) {
          serviceWorking = true;
          results.services[service.name].workingConfig = sslConfig.name;
        }
        
        console.log(`    ${result.success ? '✅' : '❌'} ${sslConfig.name}: ${result.success ? 'Success' : result.error}`);
        
      } catch (error) {
        results.services[service.name].configs[sslConfig.name] = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          responseTime: 0
        };
        console.log(`    ❌ ${sslConfig.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    if (serviceWorking) {
      results.summary.workingServices++;
    } else {
      results.summary.failingServices++;
      if (service.critical) {
        results.summary.criticalFailures++;
      }
    }
  }

  // Generate recommendations
  const recommendations = [];
  
  if (results.summary.criticalFailures > 0) {
    recommendations.push("⚠️ Critical services are failing - this will break core functionality");
  }
  
  if (results.summary.failingServices > results.summary.workingServices) {
    recommendations.push("🔧 Consider downgrading Node.js version or updating SSL configuration");
  }

  // Check if R2 specifically is failing
  const r2Result = results.services["Cloudflare R2"];
  if (r2Result && !r2Result.workingConfig) {
    recommendations.push("🔴 Cloudflare R2 SSL issues confirmed - this is the root cause of upload failures");
  }

  results.recommendations = recommendations;

  console.log(`\n📊 Test Summary:`);
  console.log(`   Working: ${results.summary.workingServices}/${results.summary.totalServices}`);
  console.log(`   Critical failures: ${results.summary.criticalFailures}`);

  return NextResponse.json(results);
}
