import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { v4 as uuidv4 } from "uuid";
import { getPublicUrl } from "@/lib/r2";
import { createR2Client, type RetryResult, type RetryOptions } from "@/lib/r2-client";

// Initialize R2 client
const r2Client = createR2Client();

// POST /api/upload/r2-direct - Upload a file directly to R2 from the server
export async function POST(request: NextRequest) {
  try {
    // Validate R2 configuration
    const requiredEnvVars = ['R2_ACCOUNT_ID', 'R2_ACCESS_KEY_ID', 'R2_SECRET_ACCESS_KEY', 'R2_BUCKET_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      console.error('Missing R2 environment variables:', missingVars);
      return NextResponse.json({
        error: "R2 configuration incomplete",
        details: `Missing: ${missingVars.join(', ')}`
      }, { status: 500 });
    }

    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const type = (formData.get("type") as string) || "profile";
    const communityId = formData.get("communityId") as string;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Determine the folder path based on the upload type
    let folder = "uploads";

    if (type === "profile") {
      folder = `profiles/${session.user.id}`;
    } else if (type === "community" && communityId) {
      folder = `communities/${communityId}`;
    } else if (type === "community-banner") {
      folder = communityId
        ? `communities/${communityId}/banners`
        : "communities/banners";
    } else if (type === "community-icon") {
      folder = communityId
        ? `communities/${communityId}/icons`
        : "communities/icons";
    } else if (type === "post-image") {
      folder = communityId
        ? `communities/${communityId}/posts/images`
        : `posts/images/${session.user.id}`;
    } else if (type === "message-image") {
      folder = `messages/images/${session.user.id}`;
    }

    // Create a unique filename
    const fileExtension = file.name.split(".").pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    const key = `${folder}/${uniqueFileName}`;

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    console.log(`Uploading file to R2: ${key} (${file.type})`);

    // Try direct upload with improved SSL configuration
    try {
      console.log('Attempting direct upload to R2 with improved SSL configuration...');

      const uploadResult = await r2Client.uploadFile(
        buffer,
        key,
        file.type,
        { maxAttempts: 3, timeoutMs: 30000, baseDelayMs: 250 }
      );

      console.log(`File uploaded successfully to R2 on attempt ${uploadResult.retry}`);

      // Get the public URL
      const fileUrl = getPublicUrl(key);

      return NextResponse.json({
        success: true,
        url: fileUrl,
        key,
        fileName: file.name,
        fileType: file.type,
        uploadMethod: 'direct'
      });

    } catch (uploadError: any) {
      console.error("Direct upload failed:", uploadError.message);

      // If direct upload fails, try presigned URL approach
      console.log('Direct upload failed, trying presigned URL approach...');

      try {
        const presignedUrl = r2Client.generatePresignedUploadUrl(key, file.type, 3600);
        console.log('Generated presigned URL for client-side upload');

        return NextResponse.json({
          success: true,
          uploadMethod: 'presigned',
          presignedUrl,
          key,
          fileName: file.name,
          fileType: file.type,
          publicUrl: getPublicUrl(key),
          message: 'Using presigned URL due to server SSL compatibility'
        });

      } catch (presignedError: any) {
        console.error("Presigned URL generation also failed:", presignedError);

        return NextResponse.json({
          error: "Upload service temporarily unavailable",
          details: "Both direct upload and presigned URL generation failed",
          suggestion: "Please try again later or contact support"
        }, { status: 503 });
      }
    }
  } catch (error: any) {
    console.error("Error uploading file:", error);

    // Provide more specific error messages based on error type
    let userMessage = "Failed to upload file";
    let statusCode = 500;

    if (error.code === "EPROTO" || error.message?.includes("SSL") || error.message?.includes("handshake")) {
      userMessage = "Upload service temporarily unavailable due to SSL configuration. Please try again later.";
      statusCode = 503; // Service Unavailable
    } else if (error.code === "ENOTFOUND" || error.code === "ECONNREFUSED") {
      userMessage = "Unable to connect to upload service. Please check your internet connection.";
      statusCode = 503;
    } else if (error.code === "ETIMEDOUT") {
      userMessage = "Upload request timed out. Please try again.";
      statusCode = 408; // Request Timeout
    }

    return NextResponse.json(
      {
        error: userMessage,
        message: error.message,
        code: error.code,
        technical: "SSL/TLS handshake failure with Cloudflare R2. This is a known issue with certain Node.js versions.",
      },
      { status: statusCode }
    );
  }
}
