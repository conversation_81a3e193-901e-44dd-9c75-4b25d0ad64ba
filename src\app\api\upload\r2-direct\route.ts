import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { v4 as uuidv4 } from "uuid";
import { getPublicUrl } from "@/lib/r2";
import {
  S3Client,
  PutObjectCommand,
  ObjectCannedACL,
} from "@aws-sdk/client-s3";

// Create an S3 client configured for R2
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.R2_ENDPOINT_URL,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
  },
});

// POST /api/upload/r2-direct - Upload a file directly to R2 from the server
export async function POST(request: NextRequest) {
  try {
    // Validate R2 configuration
    const requiredEnvVars = ['R2_ACCOUNT_ID', 'R2_ACCESS_KEY_ID', 'R2_SECRET_ACCESS_KEY', 'R2_BUCKET_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      console.error('Missing R2 environment variables:', missingVars);
      return NextResponse.json({
        error: "R2 configuration incomplete",
        details: `Missing: ${missingVars.join(', ')}`
      }, { status: 500 });
    }

    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const type = (formData.get("type") as string) || "profile";
    const communityId = formData.get("communityId") as string;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Determine the folder path based on the upload type
    let folder = "uploads";

    if (type === "profile") {
      folder = `profiles/${session.user.id}`;
    } else if (type === "community" && communityId) {
      folder = `communities/${communityId}`;
    } else if (type === "community-banner") {
      folder = communityId
        ? `communities/${communityId}/banners`
        : "communities/banners";
    } else if (type === "community-icon") {
      folder = communityId
        ? `communities/${communityId}/icons`
        : "communities/icons";
    } else if (type === "post-image") {
      folder = communityId
        ? `communities/${communityId}/posts/images`
        : `posts/images/${session.user.id}`;
    } else if (type === "message-image") {
      folder = `messages/images/${session.user.id}`;
    }

    // Create a unique filename
    const fileExtension = file.name.split(".").pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    const key = `${folder}/${uniqueFileName}`;

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    console.log(`Uploading file to R2: ${key} (${file.type})`);

    // Upload directly to R2 using AWS SDK
    try {
      console.log('Uploading file to R2 using AWS SDK...');

      const uploadCommand = new PutObjectCommand({
        Bucket: process.env.R2_BUCKET_NAME,
        Key: key,
        Body: buffer,
        ContentType: file.type,
        ACL: "public-read" as ObjectCannedACL,
      });

      await s3Client.send(uploadCommand);
      console.log('File uploaded successfully to R2');

      // Get the public URL
      const fileUrl = getPublicUrl(key);

      return NextResponse.json({
        success: true,
        url: fileUrl,
        key,
        fileName: file.name,
        fileType: file.type,
        uploadMethod: 'direct'
      });

    } catch (uploadError: any) {
      console.error("Upload failed:", uploadError.message);

      return NextResponse.json({
        error: "Failed to upload file",
        details: uploadError.message,
        suggestion: "Please try again later or contact support"
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error("Error uploading file:", error);

    return NextResponse.json(
      {
        error: "Failed to upload file",
        message: error.message,
        code: error.code,
      },
      { status: 500 }
    );
  }
}
