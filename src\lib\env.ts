import { z } from "zod";

/**
 * Environment Variables Schema
 * 
 * This file centralizes all environment variable validation using Zod.
 * It ensures that all required environment variables are present and properly formatted
 * at application startup, preventing silent configuration errors.
 */

const envSchema = z.object({
  // ============ Node Environment ============
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),

  // ============ NextAuth Configuration ============
  NEXTAUTH_SECRET: z.string().min(32, "NEXTAUTH_SECRET must be at least 32 characters long"),
  NEXTAUTH_URL: z.string().url("NEXTAUTH_URL must be a valid URL"),

  // ============ OAuth Configuration ============
  GOOGLE_CLIENT_ID: z.string().min(1, "GOOGLE_CLIENT_ID is required"),
  GOOGLE_CLIENT_SECRET: z.string().min(1, "GOOGLE_CLIENT_SECRET is required"),

  // ============ Database Configuration (MongoDB) ============
  MONGODB_URI: z.string()
    .min(1, "MONGODB_URI is required")
    .refine(
      (uri) => uri.startsWith("mongodb://") || uri.startsWith("mongodb+srv://"),
      "MONGODB_URI must be a valid MongoDB connection string"
    ),

  // ============ Cloudflare R2 Configuration ============
  R2_ACCOUNT_ID: z.string().min(1, "R2_ACCOUNT_ID is required for Cloudflare R2"),
  R2_ACCESS_KEY_ID: z.string().min(1, "R2_ACCESS_KEY_ID is required for Cloudflare R2"),
  R2_SECRET_ACCESS_KEY: z.string().min(1, "R2_SECRET_ACCESS_KEY is required for Cloudflare R2"),
  R2_BUCKET_NAME: z.string().min(1, "R2_BUCKET_NAME is required for Cloudflare R2"),
  
  // R2 Public URLs (optional, one should be provided)
  NEXT_PUBLIC_R2_PUBLIC_URL: z.string().url().optional(),
  NEXT_PUBLIC_CUSTOM_DOMAIN: z.string().url().optional(),

  // ============ Payment Gateway Configuration ============
  // Razorpay
  RAZORPAY_KEY_ID: z.string().min(1, "RAZORPAY_KEY_ID is required"),
  RAZORPAY_KEY_SECRET: z.string()
    .min(1, "RAZORPAY_KEY_SECRET is required")
    .refine(
      (secret) => !secret.includes("dummy") && !secret.includes("placeholder"),
      "RAZORPAY_KEY_SECRET cannot be a dummy or placeholder value"
    ),

  // Stripe (optional but recommended)
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),

  // ============ Email Configuration ============
  RESEND_API_KEY: z.string().optional(),
  EMAIL_FROM: z.string().email().optional(),
  
  // Legacy email configuration (optional)
  EMAIL_SERVER_HOST: z.string().optional(),
  EMAIL_SERVER_PORT: z.string().optional(),
  EMAIL_SERVER_USER: z.string().optional(),
  EMAIL_SERVER_PASSWORD: z.string().optional(),

  // ============ Application Configuration ============
  NEXT_PUBLIC_APP_NAME: z.string().default("TheTribeLab"),
  AUTO_VERIFY_EMAIL: z.string().optional(),

  // ============ ImageKit Configuration (Legacy) ============
  PRIVATE_KEY: z.string().optional(),
  NEXT_PUBLIC_PUBLIC_KEY: z.string().optional(),
  NEXT_PUBLIC_URL_ENDPOINT: z.string().optional(),

  // ============ Development/Debug Configuration ============
  // Allow additional environment variables in development
}).refine(
  (data) => {
    // Ensure at least one R2 public URL is provided
    return data.NEXT_PUBLIC_R2_PUBLIC_URL || data.NEXT_PUBLIC_CUSTOM_DOMAIN;
  },
  {
    message: "Either NEXT_PUBLIC_R2_PUBLIC_URL or NEXT_PUBLIC_CUSTOM_DOMAIN must be provided",
    path: ["NEXT_PUBLIC_R2_PUBLIC_URL"]
  }
).refine(
  (data) => {
    // If Stripe is configured, ensure webhook secret is also provided
    if (data.STRIPE_SECRET_KEY && !data.STRIPE_WEBHOOK_SECRET) {
      return false;
    }
    return true;
  },
  {
    message: "STRIPE_WEBHOOK_SECRET is required when STRIPE_SECRET_KEY is provided",
    path: ["STRIPE_WEBHOOK_SECRET"]
  }
);

// Type inference for the validated environment
export type Env = z.infer<typeof envSchema>;

/**
 * Validates and parses environment variables
 * Throws an error if validation fails
 */
function validateEnv(): Env {
  try {
    const parsed = envSchema.parse(process.env);
    return parsed;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map((err) => {
        const path = err.path.join(".");
        return `${path}: ${err.message}`;
      });

      console.error("❌ Environment variable validation failed:");
      console.error("==========================================");
      errorMessages.forEach((msg) => console.error(`  • ${msg}`));
      console.error("==========================================");
      console.error("\n💡 Please check your environment variables and ensure all required values are set.");
      console.error("📖 Refer to the project documentation for configuration details.\n");
      
      throw new Error(`Environment validation failed: ${errorMessages.join(", ")}`);
    }
    throw error;
  }
}

/**
 * Additional validation for critical configurations
 */
function validateCriticalConfigs(env: Env): void {
  // Validate TLS/SSL configuration for MongoDB
  if (env.NODE_ENV === "production" && !env.MONGODB_URI.includes("ssl=true")) {
    console.warn("⚠️  WARNING: MongoDB connection in production should use SSL (ssl=true)");
  }

  // Validate Razorpay credentials format
  if (!env.RAZORPAY_KEY_ID.startsWith("rzp_")) {
    throw new Error("RAZORPAY_KEY_ID should start with 'rzp_'");
  }

  // Validate NextAuth URL in production
  if (env.NODE_ENV === "production" && env.NEXTAUTH_URL.includes("localhost")) {
    throw new Error("NEXTAUTH_URL cannot use localhost in production");
  }

  // Check for development-only configurations in production
  if (env.NODE_ENV === "production") {
    if (env.NEXTAUTH_URL.includes("http://")) {
      console.warn("⚠️  WARNING: Using HTTP in production is not recommended. Use HTTPS.");
    }
  }
}

// Validate environment variables at module load (optional validation)
let env: Env;

// Only validate if explicitly requested via environment variable
if (process.env.VALIDATE_ENV === "true" || process.env.NODE_ENV === "production") {
  try {
    env = validateEnv();
    validateCriticalConfigs(env);
    
    if (process.env.NODE_ENV !== "test") {
      console.log("✅ Environment variables validated successfully");
    }
  } catch (error) {
    if (process.env.NODE_ENV === "production") {
      console.error("❌ Failed to initialize application due to environment variable errors");
      process.exit(1);
    } else {
      console.warn("⚠️ Environment validation failed (non-production):", error);
      env = process.env as any; // Fallback to process.env
    }
  }
} else {
  // In development/test, just use process.env directly
  console.log("ℹ️ Environment validation skipped (set VALIDATE_ENV=true to enable)");
  env = process.env as any;
}

export { env };

/**
 * Utility functions for environment-specific behavior
 */
export const isDevelopment = env.NODE_ENV === "development";
export const isProduction = env.NODE_ENV === "production";
export const isTest = env.NODE_ENV === "test";

/**
 * Get database name from MongoDB URI
 */
export const getDatabaseName = (): string => {
  try {
    const url = new URL(env.MONGODB_URI);
    return url.pathname.slice(1).split("?")[0] || "defaultdb";
  } catch {
    return "defaultdb";
  }
};

/**
 * Check if email service is configured
 */
export const isEmailConfigured = (): boolean => {
  return !!(env.RESEND_API_KEY || (env.EMAIL_SERVER_HOST && env.EMAIL_SERVER_USER && env.EMAIL_SERVER_PASSWORD));
};

/**
 * Check if Stripe is configured
 */
export const isStripeConfigured = (): boolean => {
  return !!(env.STRIPE_SECRET_KEY && env.STRIPE_WEBHOOK_SECRET);
};

/**
 * Get R2 public URL (with fallback)
 */
export const getR2PublicUrl = (): string => {
  return env.NEXT_PUBLIC_R2_PUBLIC_URL || env.NEXT_PUBLIC_CUSTOM_DOMAIN || "";
};

export default env;
