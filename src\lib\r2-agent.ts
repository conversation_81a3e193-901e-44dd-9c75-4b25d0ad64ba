import https from "https";
import crypto from "crypto";

/**
 * Creates a compatible HTTPS agent for R2 connections with optimized TLS configuration
 */
export function createR2Agent(): https.Agent {
  return new https.Agent({
    // Use TLS 1.2 and 1.3 for maximum compatibility
    minVersion: "TLSv1.2",
    maxVersion: "TLSv1.3",
    
    // Disable older SSL versions for security
    secureOptions: crypto.constants.SSL_OP_NO_SSLv2 | 
                   crypto.constants.SSL_OP_NO_SSLv3 | 
                   crypto.constants.SSL_OP_NO_TLSv1 |
                   crypto.constants.SSL_OP_NO_TLSv1_1,
    
    // Cloudflare R2 compatible cipher suites
    ciphers: [
      // TLS 1.3 ciphers (preferred)
      'TLS_AES_256_GCM_SHA384',
      'TLS_CHACHA20_POLY1305_SHA256',
      'TLS_AES_128_GCM_SHA256',
      // TLS 1.2 ciphers (fallback)
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-RSA-CHACHA20-POLY1305',
      'ECDHE-ECDSA-AES256-GCM-SHA384',
      'ECDHE-ECDSA-AES128-GCM-SHA256',
      'ECDHE-ECDSA-CHACHA20-POLY1305',
      // Additional compatibility ciphers
      'ECDHE-RSA-AES256-SHA384',
      'ECDHE-RSA-AES128-SHA256',
      'AES256-GCM-SHA384',
      'AES128-GCM-SHA256'
    ].join(':'),
    
    // Honor cipher order
    honorCipherOrder: true,
    
    // Connection settings
    keepAlive: true,
    timeout: 30000, // Reduced timeout for faster failure detection
    maxSockets: 50,
    
    // Enable SNI (Server Name Indication)
    servername: undefined, // Let Node.js auto-detect
    
    // ECDH curve configuration for better compatibility
    ecdhCurve: 'auto',
    
    // Enable certificate verification but with custom check for R2
    rejectUnauthorized: true,
    checkServerIdentity: (host: string, cert: any) => {
      // Accept Cloudflare R2 certificates
      if (host.includes('.r2.cloudflarestorage.com') ||
          host.includes('.r2.dev') ||
          host.includes('cloudflarestorage.com')) {
        return undefined; // Accept R2 certificates
      }
      // Use default verification for other hosts
      return require('tls').checkServerIdentity(host, cert);
    }
  });
}
