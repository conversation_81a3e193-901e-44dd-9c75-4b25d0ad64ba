import {
  S3Client,
  PutObjectCommand,
  ObjectCannedACL,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { NodeHttpHandler } from "@aws-sdk/node-http-handler";
import https from "https";
import crypto from "crypto";

// Create multiple HTTPS agents for different compatibility levels
const httpsAgents = [
  // Agent 1: Most compatible (for Node.js v23.3.0)
  new https.Agent({
    minVersion: "TLSv1.2",
    maxVersion: "TLSv1.2",
    secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
    ciphers: 'HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA',
    rejectUnauthorized: true,
    keepAlive: true,
    timeout: 30000,
  }),

  // Agent 2: Fallback with disabled SSL verification (last resort)
  new https.Agent({
    rejectUnauthorized: false,
    keepAlive: true,
    timeout: 30000,
  }),
];

// Try agents in order until one works
let workingAgent = httpsAgents[0];
let agentIndex = 0;

// Create an S3 client configured for R2 with custom HTTPS agent
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.R2_ENDPOINT_URL,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
  },
  requestHandler: new NodeHttpHandler({
    httpsAgent,
    connectionTimeout: 30000,
    socketTimeout: 30000,
  }),
});

/**
 * Generates a presigned URL for uploading a file to R2
 * @param key The key (path) where the file will be stored
 * @param contentType The MIME type of the file
 * @param expiresIn Number of seconds until the URL expires (default: 3600)
 * @returns A presigned URL that can be used to upload the file
 */
export async function generateUploadUrl(
  key: string,
  contentType: string,
  expiresIn: number = 3600
): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME,
    Key: key,
    ContentType: contentType,
    ACL: "public-read" as ObjectCannedACL,
  });

  try {
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn });
    return signedUrl;
  } catch (error) {
    console.error("Error generating upload URL:", error);
    throw new Error("Failed to generate upload URL");
  }
}

/**
 * Generates a public URL for accessing an uploaded file
 * @param key The key (path) of the file
 * @returns The public URL for accessing the file
 */
export function getPublicUrl(key: string): string {
  return `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL}/${key}`;
}

/**
 * Helper function to generate a unique file key
 * @param fileName Original file name
 * @param folder Folder path in R2 bucket
 * @returns Unique file key
 */
export function generateFileKey(fileName: string, folder: string = "uploads"): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const fileExtension = fileName.split('.').pop();
  const uniqueFileName = `${timestamp}-${randomString}.${fileExtension}`;
  return `${folder}/${uniqueFileName}`;
}
