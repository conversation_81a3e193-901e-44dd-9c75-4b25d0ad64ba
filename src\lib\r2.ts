import {
  S3Client,
  PutObjectCommand,
  ObjectCannedACL,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { NodeHttpHandler } from "@aws-sdk/node-http-handler";
import https from "https";
import crypto from "crypto";

// R2 SSL Workaround: Multiple fallback strategies
function createR2Client() {
  const strategies = [
    {
      name: "Disabled SSL Verification",
      agent: new https.Agent({
        rejectUnauthorized: false,
        keepAlive: true,
        timeout: 30000,
      })
    },
    {
      name: "Legacy SSL with TLS 1.0",
      agent: new https.Agent({
        minVersion: "TLSv1",
        maxVersion: "TLSv1.2",
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
        ciphers: 'ALL:!aNULL:!eNULL',
        rejectUnauthorized: false,
        keepAlive: true,
        timeout: 30000,
      })
    },
    {
      name: "No Agent (Default)",
      agent: undefined
    }
  ];

  for (const strategy of strategies) {
    try {
      console.log(`🔧 Trying R2 strategy: ${strategy.name}`);

      const client = new S3Client({
        region: "auto",
        endpoint: process.env.R2_ENDPOINT_URL,
        credentials: {
          accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
          secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
        },
        requestHandler: strategy.agent ? new NodeHttpHandler({
          httpsAgent: strategy.agent,
          connectionTimeout: 30000,
          socketTimeout: 30000,
        }) : undefined,
      });

      console.log(`✅ R2 client created with strategy: ${strategy.name}`);
      return client;
    } catch (error) {
      console.log(`❌ R2 strategy failed: ${strategy.name}`, error);
    }
  }

  throw new Error("All R2 SSL strategies failed");
}

const s3Client = createR2Client();

/**
 * Generates a presigned URL for uploading a file to R2
 * @param key The key (path) where the file will be stored
 * @param contentType The MIME type of the file
 * @param expiresIn Number of seconds until the URL expires (default: 3600)
 * @returns A presigned URL that can be used to upload the file
 */
export async function generateUploadUrl(
  key: string,
  contentType: string,
  expiresIn: number = 3600
): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME,
    Key: key,
    ContentType: contentType,
    ACL: "public-read" as ObjectCannedACL,
  });

  try {
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn });
    return signedUrl;
  } catch (error) {
    console.error("Error generating upload URL:", error);
    throw new Error("Failed to generate upload URL");
  }
}

/**
 * Generates a public URL for accessing an uploaded file
 * @param key The key (path) of the file
 * @returns The public URL for accessing the file
 */
export function getPublicUrl(key: string): string {
  return `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL}/${key}`;
}

/**
 * Helper function to generate a unique file key
 * @param fileName Original file name
 * @param folder Folder path in R2 bucket
 * @returns Unique file key
 */
export function generateFileKey(fileName: string, folder: string = "uploads"): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const fileExtension = fileName.split('.').pop();
  const uniqueFileName = `${timestamp}-${randomString}.${fileExtension}`;
  return `${folder}/${uniqueFileName}`;
}
