import {
  S3Client,
  PutObjectCommand,
  ObjectCannedACL,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { Node<PERSON>ttpHandler } from "@aws-sdk/node-http-handler";
import https from "https";
import crypto from "crypto";

// Create a compatible HTTPS agent for R2 connections
const httpsAgent = new https.Agent({
  // Use TLS 1.2 for maximum compatibility with Cloudflare R2
  minVersion: "TLSv1.2",
  maxVersion: "TLSv1.2", // Force TLS 1.2 to avoid handshake issues

  // Disable problematic SSL options
  secureOptions: crypto.constants.SSL_OP_NO_SSLv2 |
                 crypto.constants.SSL_OP_NO_SSLv3 |
                 crypto.constants.SSL_OP_NO_TLSv1 |
                 crypto.constants.SSL_OP_NO_TLSv1_1,

  // Use compatible cipher suites for Cloudflare R2
  ciphers: [
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES256-SHA384',
    'ECDHE-RSA-AES128-SHA256',
    'AES256-GCM-SHA384',
    'AES128-GCM-SHA256'
  ].join(':'),

  // Connection settings
  keepAlive: true,
  timeout: 30000,
  maxSockets: 50,

  // Enable SNI and proper certificate verification
  servername: undefined, // Let Node.js auto-detect
  rejectUnauthorized: true,
});

// Create an S3 client configured for R2 with custom HTTPS agent
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.R2_ENDPOINT_URL,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
  },
  requestHandler: new NodeHttpHandler({
    httpsAgent,
    connectionTimeout: 30000,
    socketTimeout: 30000,
  }),
});

/**
 * Generates a presigned URL for uploading a file to R2
 * @param key The key (path) where the file will be stored
 * @param contentType The MIME type of the file
 * @param expiresIn Number of seconds until the URL expires (default: 3600)
 * @returns A presigned URL that can be used to upload the file
 */
export async function generateUploadUrl(
  key: string,
  contentType: string,
  expiresIn: number = 3600
): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: process.env.R2_BUCKET_NAME,
    Key: key,
    ContentType: contentType,
    ACL: "public-read" as ObjectCannedACL,
  });

  try {
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn });
    return signedUrl;
  } catch (error) {
    console.error("Error generating upload URL:", error);
    throw new Error("Failed to generate upload URL");
  }
}

/**
 * Generates a public URL for accessing an uploaded file
 * @param key The key (path) of the file
 * @returns The public URL for accessing the file
 */
export function getPublicUrl(key: string): string {
  return `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL}/${key}`;
}

/**
 * Helper function to generate a unique file key
 * @param fileName Original file name
 * @param folder Folder path in R2 bucket
 * @returns Unique file key
 */
export function generateFileKey(fileName: string, folder: string = "uploads"): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const fileExtension = fileName.split('.').pop();
  const uniqueFileName = `${timestamp}-${randomString}.${fileExtension}`;
  return `${folder}/${uniqueFileName}`;
}
