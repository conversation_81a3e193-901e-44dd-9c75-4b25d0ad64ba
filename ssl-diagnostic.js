const https = require('https');
const tls = require('tls');
const crypto = require('crypto');

const R2_ACCOUNT_ID = 'e6e8f16fca044c21183dbe6f5007951f';
const hostname = `${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`;

console.log('=== SSL/TLS Diagnostic for R2 Connection ===\n');

console.log('Node.js version:', process.version);
console.log('OpenSSL version:', process.versions.openssl);
console.log('Target hostname:', hostname);
console.log('');

// Test 1: Basic connection without any custom config
async function testBasicConnection() {
  console.log('Test 1: Basic HTTPS connection');
  return new Promise((resolve) => {
    const req = https.request({
      hostname: hostname,
      port: 443,
      path: '/',
      method: 'GET',
      timeout: 10000,
    }, (res) => {
      console.log('✅ Basic connection successful');
      console.log('  Status:', res.statusCode);
      console.log('  TLS Version:', res.socket.getProtocol());
      console.log('  Cipher:', res.socket.getCipher());
      resolve(true);
    });

    req.on('error', (error) => {
      console.log('❌ Basic connection failed:', error.code || error.message);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('❌ Basic connection timeout');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Test 2: Get server's supported ciphers and protocols
async function testServerCapabilities() {
  console.log('\nTest 2: Server capabilities');
  return new Promise((resolve) => {
    const socket = tls.connect(443, hostname, {
      servername: hostname,
      rejectUnauthorized: false, // Just for diagnostics
    }, () => {
      console.log('✅ TLS connection established');
      console.log('  Protocol:', socket.getProtocol());
      console.log('  Cipher:', socket.getCipher());
      console.log('  Server certificate subject:', socket.getPeerCertificate().subject);
      console.log('  Server certificate issuer:', socket.getPeerCertificate().issuer);
      socket.end();
      resolve(true);
    });

    socket.on('error', (error) => {
      console.log('❌ TLS connection failed:', error.code || error.message);
      resolve(false);
    });

    socket.setTimeout(10000, () => {
      console.log('❌ TLS connection timeout');
      socket.destroy();
      resolve(false);
    });
  });
}

// Test 3: Test with different TLS versions
async function testTLSVersions() {
  console.log('\nTest 3: Testing different TLS versions');
  
  const versions = ['TLSv1.2', 'TLSv1.3'];
  
  for (const version of versions) {
    console.log(`  Testing ${version}...`);
    
    const success = await new Promise((resolve) => {
      const socket = tls.connect(443, hostname, {
        minVersion: version,
        maxVersion: version,
        servername: hostname,
        rejectUnauthorized: true,
      }, () => {
        console.log(`  ✅ ${version} successful`);
        socket.end();
        resolve(true);
      });

      socket.on('error', (error) => {
        console.log(`  ❌ ${version} failed:`, error.code || error.message);
        resolve(false);
      });

      socket.setTimeout(5000, () => {
        socket.destroy();
        resolve(false);
      });
    });
  }
}

// Test 4: Test cipher compatibility
async function testCipherCompatibility() {
  console.log('\nTest 4: Testing cipher compatibility');
  
  const cipherSuites = [
    'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256',
    'TLS_AES_256_GCM_SHA384:TLS_AES_128_GCM_SHA256',
    'ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-CHACHA20-POLY1305',
    'HIGH:!aNULL:!MD5',
    'DEFAULT'
  ];
  
  for (const ciphers of cipherSuites) {
    console.log(`  Testing cipher suite: ${ciphers.substring(0, 50)}...`);
    
    const success = await new Promise((resolve) => {
      const socket = tls.connect(443, hostname, {
        ciphers: ciphers,
        servername: hostname,
        rejectUnauthorized: true,
      }, () => {
        console.log(`  ✅ Cipher suite successful`);
        console.log(`     Used cipher:`, socket.getCipher());
        socket.end();
        resolve(true);
      });

      socket.on('error', (error) => {
        console.log(`  ❌ Cipher suite failed:`, error.code || error.message);
        resolve(false);
      });

      socket.setTimeout(5000, () => {
        socket.destroy();
        resolve(false);
      });
    });
    
    if (success) break; // Stop at first successful cipher suite
  }
}

// Test 5: Test with various Node.js HTTPS agent configurations
async function testAgentConfigurations() {
  console.log('\nTest 5: Testing different HTTPS agent configurations');
  
  const configs = [
    {
      name: 'Default agent',
      config: {}
    },
    {
      name: 'TLS 1.2 only',
      config: {
        secureProtocol: 'TLSv1_2_method'
      }
    },
    {
      name: 'TLS 1.3 preferred',
      config: {
        minVersion: 'TLSv1.2',
        maxVersion: 'TLSv1.3'
      }
    },
    {
      name: 'Minimal security options',
      config: {
        secureOptions: crypto.constants.SSL_OP_NO_SSLv2 | crypto.constants.SSL_OP_NO_SSLv3
      }
    }
  ];
  
  for (const { name, config } of configs) {
    console.log(`  Testing ${name}...`);
    
    const success = await new Promise((resolve) => {
      const agent = new https.Agent(config);
      
      const req = https.request({
        hostname: hostname,
        port: 443,
        path: '/',
        method: 'GET',
        agent: agent,
        timeout: 10000,
      }, (res) => {
        console.log(`  ✅ ${name} successful`);
        console.log(`     Protocol:`, res.socket.getProtocol());
        console.log(`     Cipher:`, res.socket.getCipher());
        resolve(true);
      });

      req.on('error', (error) => {
        console.log(`  ❌ ${name} failed:`, error.code || error.message);
        resolve(false);
      });

      req.on('timeout', () => {
        req.destroy();
        resolve(false);
      });

      req.end();
    });
    
    if (success) break; // Stop at first successful configuration
  }
}

// Run all tests
async function runDiagnostics() {
  await testBasicConnection();
  await testServerCapabilities();
  await testTLSVersions();
  await testCipherCompatibility();
  await testAgentConfigurations();
  
  console.log('\n=== Diagnostic Complete ===');
}

runDiagnostics().catch(console.error);
