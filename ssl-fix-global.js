// Global SSL fix for Node.js v23.3.0 compatibility issues
// Add this to the top of your main application files

const https = require('https');
const crypto = require('crypto');

// Override default HTTPS agent globally
const originalAgent = https.globalAgent;

// Create a more compatible HTTPS agent
const compatibleAgent = new https.Agent({
  // Force TLS 1.2 for maximum compatibility
  minVersion: 'TLSv1.2',
  maxVersion: 'TLSv1.2',
  
  // Use legacy server connect for older services
  secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
  
  // Compatible cipher suites
  ciphers: [
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES256-SHA384',
    'ECDHE-RSA-AES128-SHA256',
    'AES256-GCM-SHA384',
    'AES128-GCM-SHA256',
    'HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA'
  ].join(':'),
  
  // Connection settings
  keepAlive: true,
  timeout: 30000,
  maxSockets: 50,
  
  // Enable SNI
  servername: undefined,
  rejectUnauthorized: true,
});

// Replace global agent
https.globalAgent = compatibleAgent;

console.log('🔧 Applied global SSL compatibility fix for Node.js v23.3.0');

module.exports = { compatibleAgent };
