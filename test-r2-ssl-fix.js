/**
 * Test the improved SSL configuration for R2
 */

const https = require('https');
const crypto = require('crypto');

// R2 configuration
const R2_ACCOUNT_ID = 'e6e8f16fca044c21183dbe6f5007951f';
const R2_ACCESS_KEY_ID = 'e8c1709286897de45a17e5de2984653f';
const R2_SECRET_ACCESS_KEY = 'b61b9bc1071a3fe5f28066fbcdba47c2bf18fb281a54d4810046b7b666c108db';
const R2_BUCKET_NAME = 'thetribelab';

function createSignature(method, path, queryString = '', headers = {}, body = '') {
  const now = new Date();
  const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
  const dateStamp = amzDate.substring(0, 8);
  
  const region = 'auto';
  const service = 's3';
  
  // Create canonical headers
  const canonicalHeaders = Object.keys(headers)
    .sort()
    .map(key => `${key.toLowerCase()}:${headers[key]}`)
    .join('\n') + '\n';
  
  const signedHeaders = Object.keys(headers)
    .sort()
    .map(key => key.toLowerCase())
    .join(';');
  
  // Create canonical request
  const canonicalRequest = [
    method,
    path,
    queryString,
    canonicalHeaders,
    signedHeaders,
    sha256(body)
  ].join('\n');
  
  // Create string to sign
  const algorithm = 'AWS4-HMAC-SHA256';
  const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
  const stringToSign = [
    algorithm,
    amzDate,
    credentialScope,
    sha256(canonicalRequest)
  ].join('\n');
  
  // Calculate signature
  const signingKey = getSignatureKey(dateStamp, region, service);
  const signature = crypto.createHmac('sha256', signingKey)
    .update(stringToSign)
    .digest('hex');
  
  // Create authorization header
  const authorization = `${algorithm} Credential=${R2_ACCESS_KEY_ID}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
  
  return {
    ...headers,
    'Authorization': authorization,
    'X-Amz-Date': amzDate,
  };
}

function sha256(data) {
  return crypto.createHash('sha256').update(data).digest('hex');
}

function getSignatureKey(dateStamp, region, service) {
  const kDate = crypto.createHmac('sha256', 'AWS4' + R2_SECRET_ACCESS_KEY).update(dateStamp).digest();
  const kRegion = crypto.createHmac('sha256', kDate).update(region).digest();
  const kService = crypto.createHmac('sha256', kRegion).update(service).digest();
  const kSigning = crypto.createHmac('sha256', kService).update('aws4_request').digest();
  return kSigning;
}

async function testImprovedSSLConfig() {
  console.log('Testing improved SSL configuration for R2...');
  
  const hostname = `${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`;
  const path = `/${R2_BUCKET_NAME}/`;
  
  const headers = {
    'Host': hostname,
  };

  const signedHeaders = createSignature('GET', path, '', headers);
  
  // Test with improved SSL configuration
  const agent = new https.Agent({
    // Use TLS 1.2 and 1.3 for maximum compatibility
    minVersion: 'TLSv1.2',
    maxVersion: 'TLSv1.3',
    
    // Disable older SSL versions for security
    secureOptions: crypto.constants.SSL_OP_NO_SSLv2 | 
                   crypto.constants.SSL_OP_NO_SSLv3 | 
                   crypto.constants.SSL_OP_NO_TLSv1 |
                   crypto.constants.SSL_OP_NO_TLSv1_1,
    
    // Cloudflare R2 compatible cipher suites
    ciphers: [
      // TLS 1.3 ciphers (preferred)
      'TLS_AES_256_GCM_SHA384',
      'TLS_CHACHA20_POLY1305_SHA256',
      'TLS_AES_128_GCM_SHA256',
      // TLS 1.2 ciphers (fallback)
      'ECDHE-RSA-AES256-GCM-SHA384',
      'ECDHE-RSA-AES128-GCM-SHA256',
      'ECDHE-RSA-CHACHA20-POLY1305',
      'ECDHE-ECDSA-AES256-GCM-SHA384',
      'ECDHE-ECDSA-AES128-GCM-SHA256',
      'ECDHE-ECDSA-CHACHA20-POLY1305',
      // Additional compatibility ciphers
      'ECDHE-RSA-AES256-SHA384',
      'ECDHE-RSA-AES128-SHA256',
      'AES256-GCM-SHA384',
      'AES128-GCM-SHA256'
    ].join(':'),
    
    honorCipherOrder: true,
    keepAlive: true,
    timeout: 30000,
    maxSockets: 50,
    
    // Enable SNI (Server Name Indication)
    servername: undefined, // Let Node.js auto-detect
    
    // ECDH curve configuration for better compatibility
    ecdhCurve: 'auto',
    
    // Keep SSL verification enabled but with better compatibility
    rejectUnauthorized: true,
    checkServerIdentity: (host, cert) => {
      // Custom server identity check for R2
      if (host.includes('.r2.cloudflarestorage.com') ||
          host.includes('.r2.dev') ||
          host.includes('cloudflarestorage.com')) {
        console.log(`✅ Accepting R2 certificate for ${host}`);
        return undefined; // Accept R2 certificates
      }
      return require('tls').checkServerIdentity(host, cert);
    }
  });

  try {
    const response = await new Promise((resolve, reject) => {
      const req = https.request({
        hostname: hostname,
        path: path,
        method: 'GET',
        headers: signedHeaders,
        agent: agent,
        timeout: 30000,
      }, (res) => {
        console.log(`✅ Connection successful! Status: ${res.statusCode}`);
        console.log(`Headers:`, Object.keys(res.headers).slice(0, 5));
        
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({ statusCode: res.statusCode, data, headers: res.headers });
        });
      });
      
      req.on('error', (error) => {
        reject(error);
      });
      
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
      
      req.end();
    });
    
    console.log('🎉 SSL configuration working! R2 connection successful!');
    return true;
    
  } catch (error) {
    console.log(`❌ SSL configuration failed:`, error.message);
    if (error.code) {
      console.log(`   Error code: ${error.code}`);
    }
    return false;
  }
}

// Run the test
testImprovedSSLConfig().then((success) => {
  if (success) {
    console.log('\n✅ SUCCESS: The improved SSL configuration works!');
    console.log('   You can now use direct uploads to R2.');
  } else {
    console.log('\n❌ FAILED: SSL issues persist.');
    console.log('   Consider using presigned URLs for client-side uploads.');
  }
}).catch(console.error);
